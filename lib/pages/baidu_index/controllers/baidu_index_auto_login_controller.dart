import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:puppeteer/puppeteer.dart' hide Key;
import 'package:http/http.dart' as http;
import 'baidu_index_log_controller .dart';
import 'baidu_index_user_controller.dart';
import '../../../model/baidu_user_model.dart';
import '../baidu_index_logic.dart';

/// 网站验证登录配置
class WebsiteLoginConfig {
  final String url;
  final int loginCount;
  final int intervalSeconds;
  final String xpath;

  WebsiteLoginConfig({
    required this.url,
    required this.loginCount,
    required this.intervalSeconds,
    this.xpath = '//*[@id="app"]/main/div/div[1]/div[5]/div[2]/div/div[2]/table/tbody/tr[2]/td[4]',
  });
}

/// 网站验证登录结果
class WebsiteLoginResult {
  final bool success;
  final String message;
  final int? availableCount;
  final List<String> accountList;

  WebsiteLoginResult({
    required this.success,
    required this.message,
    this.availableCount,
    this.accountList = const [],
  });
}

/// Cookie导入登录配置
class CookieLoginConfig {
  final String cookieFilePath;
  final int intervalSeconds;

  CookieLoginConfig({
    required this.cookieFilePath,
    required this.intervalSeconds,
  });
}

/// 百度指数自动登录控制器
/// 负责处理自动登录的业务逻辑
class BaiduIndexAutoLoginController extends GetxController {

  LogController get logController => Get.find<LogController>();
  UserController get userController => Get.find<UserController>();
  BaiduIndexLogic get baiduIndexLogic => Get.find<BaiduIndexLogic>();

  // API密钥相关变量
  String userApiKey = "";
  String userApiKeyTime = "";

  /// 执行网站验证登录
  Future<WebsiteLoginResult> executeWebsiteLogin(WebsiteLoginConfig config) async {
    logController.addLog('🚀 启动网站验证自动登录模式');
    logController.addLog('🌐 访问验证网站: ${config.url}');
    logController.addLog('📊 配置信息: 登录次数=${config.loginCount}, 间隔=${config.intervalSeconds}秒');

    try {
      logController.addLog('📱 正在启动浏览器...');

      // 启动浏览器
      final browser = await puppeteer.launch(
        headless: false, // 显示浏览器窗口
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      );

      final page = await browser.newPage();

      logController.addLog('🔗 正在访问验证网站...');

      // 访问指定网站
      await page.goto(config.url, wait: Until.networkIdle);

      logController.addLog('✅ 网站加载完成，正在提取次数信息...');

      // 等待页面完全加载
      await Future.delayed(Duration(seconds: 2));

      // 提取指定XPath的文本内容
      final result = await _extractCountFromPage(page, config.xpath);

      if (result.success) {
        logController.addLog('🎯 成功提取次数信息: ${result.availableCount}');

        if (result.availableCount! > 0) {
          logController.addLog('✅ 验证成功，发现 ${result.availableCount} 个可用账号');
          
          // 执行批量登录流程
          await _executeBatchLogin(browser, config, result.availableCount!);
          
          return WebsiteLoginResult(
            success: true,
            message: '网站验证成功，发现 ${result.availableCount} 个可用账号',
            availableCount: result.availableCount,
          );
        } else {
          logController.addLog('⚠️ 未发现可用账号');
          return WebsiteLoginResult(
            success: false,
            message: '未发现可用账号',
            availableCount: 0,
          );
        }
      } else {
        return result;
      }

    } catch (e) {
      final errorMsg = '网站验证失败: $e';
      logController.addLog('❌ $errorMsg');
      return WebsiteLoginResult(
        success: false,
        message: errorMsg,
      );
    }
  }

  /// 从页面提取次数信息
  Future<WebsiteLoginResult> _extractCountFromPage(Page page, String xpath) async {
    try {
      // 使用XPath查找元素并获取文本
      final result = await page.evaluate('''
        function() {
          const xpath = '$xpath';
          const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
          const element = result.singleNodeValue;
          if (element) {
            return element.textContent.trim();
          }
          return null;
        }
      ''');

      if (result != null) {
        // 解析次数（假设格式为"15次"这样的文本）
        final countMatch = RegExp(r'(\d+)').firstMatch(result.toString());
        if (countMatch != null) {
          final count = int.parse(countMatch.group(1)!);
          return WebsiteLoginResult(
            success: true,
            message: '成功提取次数信息',
            availableCount: count,
          );
        } else {
          final errorMsg = '无法解析次数信息，原始文本: $result';
          logController.addLog('⚠️ $errorMsg');
          return WebsiteLoginResult(
            success: false,
            message: errorMsg,
          );
        }
      } else {
        final errorMsg = '未找到指定的元素，请检查XPath是否正确';
        logController.addLog('❌ $errorMsg');
        return WebsiteLoginResult(
          success: false,
          message: errorMsg,
        );
      }
    } catch (e) {
      final errorMsg = '提取次数信息失败: $e';
      logController.addLog('❌ $errorMsg');
      return WebsiteLoginResult(
        success: false,
        message: errorMsg,
      );
    }
  }

  /// 执行批量登录流程
  Future<void> _executeBatchLogin(Browser browser, WebsiteLoginConfig config, int availableCount) async {
    logController.addLog('🔄 开始批量登录流程...');

    // 检查可用次数是否足够
    if (availableCount < config.loginCount) {
      logController.addLog('⚠️ 可用账号数量不足！');
      logController.addLog('📊 可用账号数: $availableCount, 需要登录数: ${config.loginCount}');
      logController.addLog('💡 请修改登录次数为 $availableCount 或更少');
      return;
    }

    final loginCount = config.loginCount;
    logController.addLog('📝 开始登录 $loginCount 个账号（可用账号数: $availableCount）');

    for (int i = 0; i < loginCount; i++) {
      logController.addLog('🔐 正在处理第 ${i + 1} 个账号...');

      try {
        // 执行单个账号的登录流程
        final success = await _executeSingleAccountLogin(browser, config, i + 1);

        if (success) {
          logController.addLog('✅ 第 ${i + 1} 个账号登录成功');
        } else {
          logController.addLog('❌ 第 ${i + 1} 个账号登录失败');
        }

        // 如果不是最后一个账号，等待指定的时间间隔
        if (i < loginCount - 1) {
          logController.addLog('⏱️ 等待 ${config.intervalSeconds} 秒后处理下一个账号...');
          await Future.delayed(Duration(seconds: config.intervalSeconds));
        }

      } catch (e) {
        logController.addLog('❌ 第 ${i + 1} 个账号处理失败: $e');
        continue;
      }
    }

    logController.addLog('🎉 批量登录流程完成');
    logController.addLog('🔍 浏览器将保持打开状态，您可以手动查看页面内容');
    logController.addLog('💡 请手动关闭浏览器窗口以结束验证流程');
  }

  /// 执行单个账号的登录流程
  Future<bool> _executeSingleAccountLogin(Browser browser, WebsiteLoginConfig config, int accountIndex) async {
    BaiDuUsers? user;

    try {
      // 创建新的用户对象
      user = BaiDuUsers(
        time_str: DateTime.now().toString(),
        username: "登录中...",
      );

      // 添加到用户列表
      userController.users.add(user);
      baiduIndexLogic.update(['list', "two"]);

      // 创建新的无痕浏览器上下文
      final context = await browser.createIncognitoBrowserContext();
      final page = await context.newPage();

      // 设置请求拦截
      await page.setRequestInterception(true);
      await _setupRequestInterception(page, user, accountIndex);

      // 设置响应监听
      final onRequestCompleter = Completer<void>();
      await _setupResponseListener(page, user, accountIndex, onRequestCompleter, browser);

      // 访问百度指数页面
      const baiduIndexUrl = 'https://index.baidu.com/v2/main/index.html#/trend/%E5%8D%8E%E4%B8%BA?words=%E5%8D%8E%E4%B8%BA';
      logController.addLog('🌐 第 $accountIndex 个账号：访问百度指数页面...');

      await page.goto(baiduIndexUrl, wait: Until.networkIdle);

      // 等待登录窗口出现
      logController.addLog('⏳ 第 $accountIndex 个账号：等待登录窗口出现...');
      const loginButtonXPath = '//*[@id="TANGRAM__PSP_4__footerQrcodeBtn"]';

      try {
        await page.waitForXPath(loginButtonXPath, timeout: Duration(seconds: 10));
        logController.addLog('✅ 第 $accountIndex 个账号：登录窗口已出现');
      } catch (e) {
        logController.addLog('❌ 第 $accountIndex 个账号：登录窗口未出现，跳过此账号');
        await context.close();
        return false;
      }

      // 点击二维码登录按钮
      logController.addLog('🖱️ 第 $accountIndex 个账号：点击二维码登录按钮...');
      final loginButton = await page.$x(loginButtonXPath);
      if (loginButton.isNotEmpty) {
        await loginButton.first.click();
      } else {
        logController.addLog('❌ 第 $accountIndex 个账号：未找到登录按钮');
        await context.close();
        return false;
      }

      // 等待二维码图片出现
      logController.addLog('⏳ 第 $accountIndex 个账号：等待二维码图片出现...');
      const qrCodeImageXPath = '/html/body/div[13]/div/div/div[3]/div[3]/div/div/div[1]/img';

      try {
        await page.waitForXPath(qrCodeImageXPath, timeout: Duration(seconds: 10));
        logController.addLog('✅ 第 $accountIndex 个账号：二维码图片已出现');
      } catch (e) {
        logController.addLog('❌ 第 $accountIndex 个账号：二维码图片未出现，跳过此账号');
        await context.close();
        return false;
      }

      // 提取二维码图片的src链接
      final qrCodeSrc = await _extractQrCodeSrc(page, qrCodeImageXPath, accountIndex);
      if (qrCodeSrc == null) {
        await context.close();
        return false;
      }

      // 执行自动登录处理
      final loginSuccess = await _processAutoLogin(browser, config, qrCodeSrc, accountIndex);
      if (!loginSuccess) {
        await context.close();
        return false;
      }

      // 获取 wrr 和 wrrtime
      await _extractApiKeys(page, user, accountIndex);

      // 等待登录结果
      logController.addLog('⏳ 第 $accountIndex 个账号：等待登录结果...');
      await onRequestCompleter.future;

      // 关闭当前上下文
      await context.close();

      return user.isStart == true;

    } catch (e) {
      logController.addLog('❌ 第 $accountIndex 个账号登录流程异常: $e');
      if (user != null) {
        user.isError = true;
        user.username = "登录异常";
        baiduIndexLogic.update(['list', "two"]);
      }
      return false;
    }
  }

  /// 提取二维码图片的src链接
  Future<String?> _extractQrCodeSrc(Page page, String xpath, int accountIndex) async {
    try {
      logController.addLog('🔍 第 $accountIndex 个账号：提取二维码链接...');

      final qrCodeElements = await page.$x(xpath);
      if (qrCodeElements.isEmpty) {
        logController.addLog('❌ 第 $accountIndex 个账号：未找到二维码图片元素');
        return null;
      }

      final srcValue = await page.evaluate('''
        function() {
          const xpath = '$xpath';
          const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
          const element = result.singleNodeValue;
          if (element && element.src) {
            return element.src;
          }
          return null;
        }
      ''');

      if (srcValue != null) {
        logController.addLog('✅ 第 $accountIndex 个账号：成功提取二维码链接');
        logController.addLog('🔗 二维码链接: $srcValue');
        return srcValue.toString();
      } else {
        logController.addLog('❌ 第 $accountIndex 个账号：二维码链接提取失败');
        return null;
      }
    } catch (e) {
      logController.addLog('❌ 第 $accountIndex 个账号：提取二维码链接异常: $e');
      return null;
    } 
  }

  /// 执行自动登录处理
  Future<bool> _processAutoLogin(Browser browser, WebsiteLoginConfig config, String qrCodeSrc, int accountIndex) async {
    try {
      logController.addLog('🔄 第 $accountIndex 个账号：开始自动登录处理...');

      // 创建新页面访问验证网站
      final verifyPage = await browser.newPage();
      await verifyPage.goto(config.url, wait: Until.networkIdle);

      // 检查并关闭所有弹窗对话框
      await _closeAllDialogs(verifyPage, accountIndex);

      // 点击"二维码链接登录"选项（带重试机制）
      final qrCodeLoginSuccess = await _clickQrCodeLoginOption(verifyPage, accountIndex);
      if (!qrCodeLoginSuccess) {
        logController.addLog('⚠️ 第 $accountIndex 个账号：二维码链接登录选项处理失败，但继续尝试登录流程');
        // 不关闭页面，继续尝试后续流程
      }

      // 点击登录按钮
      logController.addLog('🖱️ 第 $accountIndex 个账号：点击验证网站登录按钮...');
      const verifyLoginButtonXPath = '//*[@id="app"]/main/div/div[1]/div[5]/div[3]/div[2]/div[2]/div[2]/div[1]';

      try {
        await verifyPage.waitForXPath(verifyLoginButtonXPath, timeout: Duration(seconds: 10));
        final verifyLoginButton = await verifyPage.$x(verifyLoginButtonXPath);
        if (verifyLoginButton.isNotEmpty) {
          await verifyLoginButton.first.click();
        } else {
          logController.addLog('❌ 第 $accountIndex 个账号：未找到验证网站登录按钮');
          await verifyPage.close();
          return false;
        }
      } catch (e) {
        logController.addLog('❌ 第 $accountIndex 个账号：验证网站登录按钮未出现');
        await verifyPage.close();
        return false;
      }

      // 等待输入框出现
      logController.addLog('⏳ 第 $accountIndex 个账号：等待输入框出现...');
      const headerXPath = '//*[@id="app"]/main/div/div[6]/div/div/header/span';

      try {
        await verifyPage.waitForXPath(headerXPath, timeout: Duration(seconds: 10));
        logController.addLog('✅ 第 $accountIndex 个账号：输入框区域已出现');
      } catch (e) {
        logController.addLog('❌ 第 $accountIndex 个账号：输入框区域未出现');
        // await verifyPage.close();
        return false;
      }

      // 在输入框中输入二维码链接
      logController.addLog('⌨️ 第 $accountIndex 个账号：输入二维码链接...');
      const inputSelector = 'input.el-input__inner[type="text"]';

      try {
        await verifyPage.waitForSelector(inputSelector, timeout: Duration(seconds: 10));

        // 先点击输入框获得焦点
        await verifyPage.click(inputSelector);
        logController.addLog('🖱️ 第 $accountIndex 个账号：已点击输入框获得焦点');

        // 等待输入框激活
        await Future.delayed(Duration(milliseconds: 500));

        // 清空输入框（如果有默认值）
        await verifyPage.evaluate('''
          function() {
            const input = document.querySelector('$inputSelector');
            if (input) {
              input.value = '';
              input.focus();
            }
          }
        ''');

        // 等待清空完成
        await Future.delayed(Duration(milliseconds: 300));

        // 使用JavaScript直接设置值
        await verifyPage.evaluate('''
          function(value) {
            const input = document.querySelector('$inputSelector');
            if (input) {
              input.value = value;
              // 触发input事件，确保Vue组件能检测到变化
              const event = new Event('input', { bubbles: true });
              input.dispatchEvent(event);
              // 触发change事件
              const changeEvent = new Event('change', { bubbles: true });
              input.dispatchEvent(changeEvent);
            }
          }
        ''', args: [qrCodeSrc]);

        logController.addLog('✅ 第 $accountIndex 个账号：二维码链接输入完成');
        logController.addLog('🔗 输入的链接: $qrCodeSrc');

        // 验证输入是否成功
        final inputValue = await verifyPage.evaluate('''
          function() {
            const input = document.querySelector('$inputSelector');
            return input ? input.value : '';
          }
        ''');

        if (inputValue == qrCodeSrc) {
          logController.addLog('✅ 第 $accountIndex 个账号：输入验证成功');
        } else {
          logController.addLog('⚠️ 第 $accountIndex 个账号：输入验证失败，期望: $qrCodeSrc，实际: $inputValue');
        }

      } catch (e) {
        logController.addLog('❌ 第 $accountIndex 个账号：输入框处理失败: $e');
        // 不关闭页面，继续尝试后续步骤
      }

      // 点击"立即登录"按钮
      logController.addLog('🖱️ 第 $accountIndex 个账号：点击立即登录按钮...');
      const submitButtonSelector = 'button.el-button.el-button--primary';

      try {
        await verifyPage.waitForSelector(submitButtonSelector, timeout: Duration(seconds: 10));
        // await verifyPage.click(submitButtonSelector);
        logController.addLog('✅ 第 $accountIndex 个账号：立即登录按钮已点击');
      } catch (e) {
        logController.addLog('❌ 第 $accountIndex 个账号：立即登录按钮未找到或点击失败');
        // await verifyPage.close();
        return false;
      }

      // 监听登录结果（类似loginAccount函数的逻辑）
      logController.addLog('⏳ 第 $accountIndex 个账号：监听登录结果...');

      // TODO: 这里需要添加类似loginAccount函数的监听逻辑
      // 监听网络请求、提取cookie、验证登录状态等

      // 等待一段时间让登录完成
      await Future.delayed(Duration(seconds: 5));

      // await verifyPage.close();
      logController.addLog('✅ 第 $accountIndex 个账号：自动登录处理完成');

      return true;

    } catch (e) {
      logController.addLog('❌ 第 $accountIndex 个账号：自动登录处理异常: $e');
      return false;
    }
  }

  /// 设置请求拦截
  Future<void> _setupRequestInterception(Page page, BaiDuUsers user, int accountIndex) async {
    page.onRequest.listen((request) async {
      if (request.url.contains('https://dlswbr.baidu.com/heicha/mm/2057/acs-2057.js')) {
        logController.addLog('🔍 第 $accountIndex 个账号：拦截到关键JS请求');

        var response = await http.get(Uri.parse(request.url));
        if (response.statusCode == 200) {
          var originalJsContent = response.body;

          // 定义正则表达式
          RegExp regex1 = RegExp(r"a8\(b\('([^']+)'\)\+ae\+");
          RegExp regex2 = RegExp(r"a8\('([^']+)'\s*\+\s*ae");

          String timeKet = "";
          String modifiedJsContent = "";

          // 封装匹配逻辑
          String matchDynamicValue(String content, RegExp regex) {
            Match? match = regex.firstMatch(content);
            if (match != null) {
              return match.group(1)!; // 提取动态值
            }
            return ""; // 未找到匹配的值
          }

          // 先匹配第一个字符串
          timeKet = matchDynamicValue(originalJsContent, regex1);
          // 如果第一个字符串没有匹配到，则匹配第二个字符串
          if (timeKet.isEmpty) {
            timeKet = matchDynamicValue(originalJsContent, regex2);
            logController.addLog("第 $accountIndex 个账号：regex2----$timeKet");
            modifiedJsContent = originalJsContent.replaceFirst(
              "function ek(){",  // 查找目标函数 eg
              '''
            window['wrrtime']= '$timeKet';
            window['wrr'] = a0;
            function ek(){
          ''',  // 在函数头部插入日志
            );
          } else {
            timeKet = "b('$timeKet')";
            modifiedJsContent = originalJsContent.replaceFirst(
              "function ek(){",  // 查找目标函数 eg
              '''
            window['wrrtime']= $timeKet;
            window['wrr'] = a0;
            function ek(){
          ''',  // 在函数头部插入日志
            );
          }

          // 输出结果
          if (timeKet.isNotEmpty) {
            logController.addLog('第 $accountIndex 个账号：匹配到的动态值: $timeKet');
          } else {
            logController.addLog('第 $accountIndex 个账号：未找到匹配的值');
          }

          await request.respond(
            status: 200,
            contentType: 'application/javascript',
            body: modifiedJsContent,
          );
        } else {
          // 如果获取原始文件失败，继续请求
          await request.continueRequest();
        }
      } else {
        // 对其他请求，继续请求
        await request.continueRequest();
      }
    });
  }

  /// 设置响应监听
  Future<void> _setupResponseListener(Page page, BaiDuUsers user, int accountIndex, Completer<void> onRequestCompleter, Browser browser) async {
    page.onResponse.listen((response) async {
      if (response.url.contains("https://index.baidu.com/api/SearchApi/index")) {
        logController.addLog('🎯 第 $accountIndex 个账号：检测到登录响应');

        if (response.status == 200) {
          // 获取特定 URL 的 cookies
          final cookies = await page.cookies(urls: ['https://index.baidu.com/api/SearchApi/index']);
          logController.addLog('🍪 第 $accountIndex 个账号：获取到Cookies');

          for (var cookie in cookies) {
            if (cookie.name == 'BDUSS') {
              logController.addLog('第 $accountIndex 个账号：Cookie: Name = ${cookie.name}, Value = ${cookie.value}');
              user.cookie = "${cookie.name}=${cookie.value};";
            }

            if (cookie.name == 'ab_sr') {
              user.cookie += "${cookie.name}=${cookie.value};";
            }
          }
          logController.addLog('第 $accountIndex 个账号：完整Cookie: ${user.cookie}');

          // 等待元素加载并获取用户名
          try {
            await page.waitForSelector('.username-text', timeout: Duration(seconds: 5));
            var username = await page.evaluate('''() => {
             const element = document.querySelector('.username-text');
             return element ? element.innerText : null;
             }''');

            logController.addLog('第 $accountIndex 个账号：Username: $username');
            user.username = username ?? "未知用户";
          } catch (e) {
            logController.addLog('第 $accountIndex 个账号：获取用户名失败: $e');
            user.username = "获取失败";
          }

          // 判断登录状态
          await Future.delayed(Duration(milliseconds: 200));
          final body = await response.text;
          final jsonBody = jsonDecode(body) as Map<String, dynamic>;

          // 正确字段提取
          final status = jsonBody['status'] ?? -1;
          final data = jsonBody['data']?.toString() ?? '';
          final message = jsonBody['message']?.toString() ?? '无错误信息';

          logController.addLog('第 $accountIndex 个账号：状态码: $status');
          logController.addLog('第 $accountIndex 个账号：数据: $data');
          logController.addLog('第 $accountIndex 个账号：消息: $message');

          if (data == "" && message == "request block") {
            user.isStart = false;
            user.isError = true;
            logController.addLog('❌ 第 $accountIndex 个账号：账号被封禁');
          } else {
            user.isStart = true;
            user.isError = false;
            logController.addLog('✅ 第 $accountIndex 个账号：登录成功');
          }

          baiduIndexLogic.update(['list', "two"]);
        } else {
          logController.addLog("❌ 第 $accountIndex 个账号：当前账户不可用");
          user.isStart = false;
          user.isError = true;
        }

        // 完成后标记 Completer 完成
        if (!onRequestCompleter.isCompleted) {
          onRequestCompleter.complete();
        }
      }
    });
  }

  /// 关闭所有弹窗对话框
  Future<void> _closeAllDialogs(Page page, int accountIndex) async {
    try {
      logController.addLog('🔍 第 $accountIndex 个账号：检查并关闭弹窗对话框...');

      // 等待页面加载完成
      await Future.delayed(Duration(seconds: 1));

      // 定义多种类型的关闭按钮选择器
      final closeButtonSelectors = [
        'i.el-icon.el-dialog__close',      // 对话框关闭按钮
        'i.el-icon.el-message-box__close', // 消息框关闭按钮
      ];

      int totalClosed = 0;

      // 遍历所有类型的关闭按钮
      for (String selector in closeButtonSelectors) {
        try {
          final closeButtons = await page.$$(selector);

          if (closeButtons.isNotEmpty) {
            logController.addLog('🔍 第 $accountIndex 个账号：发现 ${closeButtons.length} 个 ${selector} 类型的弹窗');

            // 逐个点击关闭按钮
            for (int i = 0; i < closeButtons.length; i++) {
              try {
                await closeButtons[i].click();
                totalClosed++;
                logController.addLog('✅ 第 $accountIndex 个账号：已关闭 ${selector} 类型的第 ${i + 1} 个弹窗');

                // 等待弹窗关闭动画完成
                await Future.delayed(Duration(milliseconds: 500));
              } catch (e) {
                logController.addLog('⚠️ 第 $accountIndex 个账号：关闭 ${selector} 类型的第 ${i + 1} 个弹窗失败: $e');
              }
            }
          }
        } catch (e) {
          logController.addLog('⚠️ 第 $accountIndex 个账号：查找 ${selector} 类型弹窗时出现异常: $e');
        }
      }

      // 查找并点击"不再通知"按钮
      await _clickNoMoreNotificationButtons(page, accountIndex);

      if (totalClosed > 0) {
        logController.addLog('✅ 第 $accountIndex 个账号：总共关闭了 $totalClosed 个弹窗');

        // 再次等待确保所有弹窗都已关闭
        await Future.delayed(Duration(seconds: 1));
      } else {
        logController.addLog('✅ 第 $accountIndex 个账号：未发现任何弹窗对话框');
      }

    } catch (e) {
      logController.addLog('⚠️ 第 $accountIndex 个账号：关闭弹窗过程中出现异常: $e');
    }
  }

  /// 点击"二维码链接登录"选项（带重试机制）
  Future<bool> _clickQrCodeLoginOption(Page page, int accountIndex) async {
    logController.addLog('🖱️ 第 $accountIndex 个账号：点击"二维码链接登录"选项...');

    const maxRetries = 3;

    for (int retry = 0; retry < maxRetries; retry++) {
      try {
        logController.addLog('🔄 第 $accountIndex 个账号：尝试第 ${retry + 1} 次查找登录选项...');

        // 等待页面元素加载
        await Future.delayed(Duration(seconds: 2));

        // 查找包含"二维码链接登录"文本的div.item
        final qrCodeLoginOption = await page.evaluate('''
          function() {
            const items = document.querySelectorAll('div.item');
            for (let item of items) {
              const titleElement = item.querySelector('.t');
              if (titleElement && titleElement.textContent.includes('二维码链接登录')) {
                return item;
              }
            }
            return null;
          }
        ''');

        if (qrCodeLoginOption != null) {
          await page.evaluate('''
            function(element) {
              element.click();
            }
          ''', args: [qrCodeLoginOption]);

          logController.addLog('✅ 第 $accountIndex 个账号：已点击"二维码链接登录"选项');

          // 等待选项点击效果完成
          await Future.delayed(Duration(seconds: 1));

          // 再次检查并点击"不再通知"按钮（可能在选择登录方式后出现）
          await _clickNoMoreNotificationButtons(page, accountIndex);

          return true;
        } else {
          logController.addLog('⚠️ 第 $accountIndex 个账号：第 ${retry + 1} 次未找到"二维码链接登录"选项');
          if (retry < maxRetries - 1) {
            logController.addLog('🔄 第 $accountIndex 个账号：等待 2 秒后重试...');
            await Future.delayed(Duration(seconds: 2));
          }
        }
      } catch (e) {
        logController.addLog('⚠️ 第 $accountIndex 个账号：第 ${retry + 1} 次查找登录选项异常: $e');
        if (retry < maxRetries - 1) {
          logController.addLog('🔄 第 $accountIndex 个账号：等待 2 秒后重试...');
          await Future.delayed(Duration(seconds: 2));
        }
      }
    }

    logController.addLog('❌ 第 $accountIndex 个账号：经过 $maxRetries 次尝试仍未找到"二维码链接登录"选项');
    return false;
  }

  /// 点击"不再通知"按钮
  Future<void> _clickNoMoreNotificationButtons(Page page, int accountIndex) async {
    try {
      logController.addLog('🔍 第 $accountIndex 个账号：查找"不再通知"按钮...');

      // 查找"不再通知"按钮的选择器
      const noMoreNotificationSelector = 'button.el-button';

      // 获取所有按钮元素
      final buttons = await page.$$(noMoreNotificationSelector);

      int clickedCount = 0;

      for (final button in buttons) {
        try {
          // 获取按钮的文本内容
          final buttonText = await page.evaluate('''
            function(element) {
              const span = element.querySelector('span');
              return span ? span.textContent.trim() : '';
            }
          ''', args: [button]);

          // 检查是否是"不再通知"按钮
          if (buttonText == '不再通知') {
            logController.addLog('🖱️ 第 $accountIndex 个账号：发现"不再通知"按钮，正在点击...');

            await button.click();
            clickedCount++;

            logController.addLog('✅ 第 $accountIndex 个账号：已点击"不再通知"按钮');

            // 等待按钮点击效果完成
            await Future.delayed(Duration(milliseconds: 500));
          }
        } catch (e) {
          logController.addLog('⚠️ 第 $accountIndex 个账号：处理按钮时出现异常: $e');
        }
      }

      if (clickedCount > 0) {
        logController.addLog('✅ 第 $accountIndex 个账号：总共点击了 $clickedCount 个"不再通知"按钮');
      } else {
        logController.addLog('✅ 第 $accountIndex 个账号：未发现"不再通知"按钮');
      }

    } catch (e) {
      logController.addLog('⚠️ 第 $accountIndex 个账号：查找"不再通知"按钮时出现异常: $e');
    }
  }

  /// 提取API密钥
  Future<void> _extractApiKeys(Page page, BaiDuUsers user, int accountIndex) async {
    try {
      logController.addLog('🔑 第 $accountIndex 个账号：提取API密钥...');

      // 获取 wrr 和 wrrtime
      var wrr = await page.evaluate('''
        function() {
          return window['wrr'];
        }
      ''');

      var wrrtime = await page.evaluate('''
        function() {
          return window['wrrtime'];
        }
      ''');

      if (wrr != null && wrr != "") {
        userApiKey = wrr.toString();
        user.apiKey = wrr.toString();
      } else {
        user.apiKey = userApiKey;
      }

      if (wrrtime != null && wrrtime != "") {
        userApiKeyTime = wrrtime.toString();
        user.apiKeyTime = wrrtime.toString().replaceAll('_', '');
      } else {
        user.apiKeyTime = userApiKeyTime.replaceAll('_', '');
      }

      logController.addLog('第 $accountIndex 个账号：API Key: ${user.apiKey}');
      logController.addLog('第 $accountIndex 个账号：API Key Time: ${user.apiKeyTime}');

    } catch (e) {
      logController.addLog('❌ 第 $accountIndex 个账号：提取API密钥失败: $e');
    }
  }

  /// 执行Cookie导入登录
  Future<WebsiteLoginResult> executeCookieLogin(CookieLoginConfig config) async {
    logController.addLog('🍪 启动Cookie导入自动登录模式');
    logController.addLog('📁 Cookie文件路径: ${config.cookieFilePath}');

    // TODO: 实现Cookie导入登录逻辑
    logController.addLog('📝 注意：Cookie导入登录逻辑将由用户后续补充');

    return WebsiteLoginResult(
      success: false,
      message: 'Cookie导入登录功能尚未实现',
    );
  }
}
